name: "Test private task for databend query"
description: "Test private task for databend query"
runs:
  using: "composite"
  steps:
    - uses: ./.github/actions/setup_test

    - name: Install lsof
      shell: bash
      run: sudo apt-get update -yq && sudo apt-get install -yq lsof

    - name: Minio Setup for (ubuntu-latest only)
      shell: bash
      run: |
        docker run -d --network host --name minio \
                -e "MINIO_ACCESS_KEY=minioadmin" \
                -e "MINIO_SECRET_KEY=minioadmin" \
                -e "MINIO_ADDRESS=:9900" \
                -v /tmp/data:/data \
                -v /tmp/config:/root/.minio \
                minio/minio server /data

        export AWS_ACCESS_KEY_ID=minioadmin
        export AWS_SECRET_ACCESS_KEY=minioadmin
        export AWS_EC2_METADATA_DISABLED=true

        aws --endpoint-url http://127.0.0.1:9900/ s3 mb s3://testbucket
        aws --endpoint-url http://127.0.0.1:9900/ s3 cp tests/data s3://testbucket/data  --recursive --no-progress

    - name: Run Private Task Tests
      shell: bash
      run: |
        bash ./tests/task/test-private-task.sh

    - name: Upload failure
      if: failure()
      uses: ./.github/actions/artifact_failure
      with:
        name: test-tasks
