name: Linux

on:
  workflow_call:
    inputs:
      build_profile:
        description: "Build profile, debug or release"
        type: string
        required: true
        default: "debug"
      runner_provider:
        description: "Self-hosted runner provider, aws or gcp"
        type: string
        required: true
        default: "aws"
      license_type:
        description: "License type, enterprise or trial"
        type: string
        required: true
        default: "trial"

env:
  BUILD_PROFILE: ${{ inputs.build_profile }}
  RUNNER_PROVIDER: ${{ inputs.runner_provider }}

jobs:
  check:
    runs-on:
      - self-hosted
      - X64
      - Linux
      - 8c32g
      - "${{ inputs.runner_provider }}"
    steps:
      - uses: actions/checkout@v4
        with:
          # fetch all tags, metasrv and metaclient need tag as its version.
          fetch-depth: 0
      - uses: ./.github/actions/check
        timeout-minutes: 60
        with:
          github_token: ${{ github.token }}

  build:
    runs-on:
      - self-hosted
      - "${{ matrix.runner }}"
      - Linux
      - 16c64g
      - "${{ inputs.runner_provider }}"
    strategy:
      fail-fast: false
      matrix:
        include:
          - { arch: x86_64, runner: X64 }
          # - { arch: aarch64, runner: ARM64 }
    steps:
      - uses: actions/checkout@v4
        with:
          # fetch all tags, metasrv and meta client need tag as its version.
          fetch-depth: 0
      - uses: ./.github/actions/build_linux
        timeout-minutes: 60
        env:
          DATABEND_ENTERPRISE_LICENSE_PUBLIC_KEY: ${{ secrets.DATABEND_ENTERPRISE_LICENSE_PUBLIC_KEY }}
        with:
          sha: ${{ github.sha }}
          target: ${{ matrix.arch }}-unknown-linux-gnu
          features: python-udf,storage-hdfs
          category: full
          artifacts: all
      - name: upload sccache log
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: sccache-build-${{ matrix.arch }}
          path: target/sccache.log

  # build_address_sanitizer:
  #   runs-on:
  #     - self-hosted
  #     - "${{ matrix.runner }}"
  #     - Linux
  #     - 8c32g
  #     - "${{ inputs.runner_provider }}"
  #   strategy:
  #     fail-fast: false
  #     matrix:
  #       include:
  #         - { arch: aarch64, runner: ARM64 }
  #   steps:
  #     - uses: actions/checkout@v4
  #       with:
  #         # fetch all tags, metasrv and metaclient need tag as its version.
  #         fetch-depth: 0
  #     - uses: ./.github/actions/build_linux_sanitizer
  #       timeout-minutes: 60
  #       with:
  #         target: ${{ matrix.arch }}-unknown-linux-gnu
  #         artifacts: query

  test_unit:
    runs-on:
      - self-hosted
      - X64
      - Linux
      - 16c64g
      - "${{ inputs.runner_provider }}"
    steps:
      - uses: actions/checkout@v4
        with:
          # fetch all tags, metasrv and metaclient need tag as its version.
          fetch-depth: 0
      - uses: ./.github/actions/test_unit
        timeout-minutes: 60

  test_metactl:
    needs: [build, check]
    runs-on:
      - self-hosted
      - X64
      - Linux
      - 2c8g
      - "${{ inputs.runner_provider }}"
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/test_metactl
        timeout-minutes: 10

  test_compat_meta_query:
    needs: [build, check]
    runs-on:
      - self-hosted
      - X64
      - Linux
      - 2c8g
      - "${{ inputs.runner_provider }}"
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/test_compat_meta_query
        timeout-minutes: 10

  test_compat_fuse:
    needs: [build, check]
    runs-on:
      - self-hosted
      - X64
      - Linux
      - 2c8g
      - "${{ inputs.runner_provider }}"
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/test_compat_fuse
        timeout-minutes: 20

  test_compat_meta_meta:
    needs: [build, check]
    runs-on:
      - self-hosted
      - X64
      - Linux
      - 2c8g
      - "${{ inputs.runner_provider }}"
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/test_compat_meta_meta
        timeout-minutes: 20

  test_logs:
    needs: [build, check]
    runs-on:
      - self-hosted
      - X64
      - Linux
      - 2c8g
      - "${{ inputs.runner_provider }}"
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup_license
        with:
          runner_provider: ${{ inputs.runner_provider }}
          type: ${{ inputs.license_type }}
      - uses: ./.github/actions/test_logs
        timeout-minutes: 20

  test_private_tasks:
    needs: [ build, check ]
    runs-on:
      - self-hosted
      - X64
      - Linux
      - 2c8g
      - "${{ inputs.runner_provider }}"
      - "${{ inputs.runner_capacity }}"
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup_license
        with:
          runner_provider: ${{ inputs.runner_provider }}
          type: ${{ inputs.license_type }}
      - uses: ./.github/actions/test_private_tasks
        timeout-minutes: 20

  test_meta_cluster:
    needs: [build, check]
    runs-on:
      - self-hosted
      - X64
      - Linux
      - 2c8g
      - "${{ inputs.runner_provider }}"
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/test_meta_cluster
        timeout-minutes: 10

  test_stateless_standalone:
    needs: [build, check]
    runs-on:
      - self-hosted
      - X64
      - Linux
      - 2c8g
      - "${{ inputs.runner_provider }}"
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/test_stateless_standalone_linux
        timeout-minutes: 18

  test_stateless_cluster:
    needs: [build, check]
    runs-on:
      - self-hosted
      - X64
      - Linux
      - 2c8g
      - "${{ inputs.runner_provider }}"
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup_license
        with:
          runner_provider: ${{ inputs.runner_provider }}
          type: ${{ inputs.license_type }}
      - uses: ./.github/actions/test_stateless_cluster_linux
        timeout-minutes: 18

  test_stateful_standalone:
    needs: [build, check]
    runs-on:
      - self-hosted
      - X64
      - Linux
      - 2c8g
      - "${{ inputs.runner_provider }}"
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/test_stateful_standalone_linux
        timeout-minutes: 10
      - name: Upload failure
        if: failure()
        uses: ./.github/actions/artifact_failure
        with:
          name: test-stateful-standalone-linux

  test_stateful_cluster:
    needs: [build, check]
    runs-on:
      - self-hosted
      - X64
      - Linux
      - 2c8g
      - "${{ inputs.runner_provider }}"
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup_license
        with:
          runner_provider: ${{ inputs.runner_provider }}
          type: ${{ inputs.license_type }}
      - uses: ./.github/actions/test_stateful_cluster_linux
        timeout-minutes: 15
      - name: Upload failure
        if: failure()
        uses: ./.github/actions/artifact_failure
        with:
          name: test-stateful-cluster-linux

  test_stateful_large_data:
    if: contains(github.event.pull_request.labels.*.name, 'ci-largedata')
    needs: [build, check]
    runs-on:
      - self-hosted
      - X64
      - Linux
      - 2c8g
      - "${{ inputs.runner_provider }}"
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/test_stateful_large_data
        timeout-minutes: 60

  test_stateful_iceberg_catalogs:
    needs: [build, check]
    runs-on:
      - self-hosted
      - X64
      - Linux
      - 2c8g
      - "${{ inputs.runner_provider }}"
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/test_stateful_iceberg_catalogs_standalone
        timeout-minutes: 10
      - name: Upload failure
        if: failure()
        uses: ./.github/actions/artifact_failure
        with:
          name: test-stateful-iceberg-catalogs-standalone

  test_stateful_hive_standalone:
    needs: [build, check]
    runs-on:
      - self-hosted
      - X64
      - Linux
      - 2c8g
      - "${{ inputs.runner_provider }}"
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/test_stateful_hive_standalone
        timeout-minutes: 10
      - name: Upload failure
        if: failure()
        uses: ./.github/actions/artifact_failure
        with:
          name: test_stateful_hive_standalone

  # test_fuzz_standalone:
  #   needs: [build, check]
  #   runs-on:
  #     - self-hosted
  #     - X64
  #     - Linux
  #     - 2c8g
  #     - "${{ inputs.runner_provider }}"
  #   steps:
  #     - uses: actions/checkout@v4
  #     - uses: ./.github/actions/test_fuzz_standalone_linux
  #       timeout-minutes: 10
  #       continue-on-error: true

  test_ee_standalone:
    needs: [build, check]
    runs-on:
      - self-hosted
      - X64
      - Linux
      - 2c8g
      - "${{ inputs.runner_provider }}"
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup_license
        with:
          runner_provider: ${{ inputs.runner_provider }}
          type: ${{ inputs.license_type }}
      - uses: ./.github/actions/test_ee_standalone_linux
        timeout-minutes: 10
      - name: Upload failure
        if: failure()
        uses: ./.github/actions/artifact_failure
        with:
          name: test-stateful-standalone-linux

  #  Temporarily commented out, since this job is run on self-hosted, may
  #  bring troubles to other jobs run on self-hosted.
  #
  #  Maybe we could patch `libfaketime` and let it support jemalloc?
  #
  #  test_ee_standalone_fake_time:
  #    needs: [build, check]
  #    runs-on:
  #      - self-hosted
  #      - X64
  #      - Linux
  #      - 2c8g
  #      - "${{ inputs.runner_provider }}"
  #    steps:
  #      - uses: actions/checkout@v4
  #      - uses: ./.github/actions/setup_license
  #        with:
  #          runner_provider: ${{ inputs.runner_provider }}
  #          type: ${{ inputs.license_type }}
  #      - uses: ./.github/actions/test_ee_standalone_fake_time_linux
  #        timeout-minutes: 10
  #      - name: Upload failure
  #        if: failure()
  #        uses: ./.github/actions/artifact_failure
  #        with:
  #          name: test-stateful-standalone-fake-time-linux

  test_ee_management_mode:
    needs: [build, check]
    runs-on:
      - self-hosted
      - X64
      - Linux
      - 2c8g
      - "${{ inputs.runner_provider }}"
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup_bendsql
      - uses: ./.github/actions/setup_license
        with:
          runner_provider: ${{ inputs.runner_provider }}
          type: ${{ inputs.license_type }}
      - uses: ./.github/actions/test_ee_management_mode_linux
        timeout-minutes: 10
      - name: Upload failure
        if: failure()
        uses: ./.github/actions/artifact_failure
        with:
          name: test-ee-management-mode-linux

  sqllogic:
    needs: [build, check]
    uses: ./.github/workflows/reuse.sqllogic.yml
    secrets: inherit
    with:
      build_profile: ${{ inputs.build_profile }}
      runner_provider: ${{ inputs.runner_provider }}
      license_type: ${{ inputs.license_type }}
