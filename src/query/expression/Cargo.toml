[package]
name = "databend-common-expression"
version = { workspace = true }
authors = { workspace = true }
license = { workspace = true }
publish = { workspace = true }
edition = { workspace = true }

[dependencies]
arrow-array = { workspace = true }
arrow-buffer = { workspace = true }
arrow-cast = { workspace = true }
arrow-data = { workspace = true }
arrow-flight = { workspace = true }
arrow-ipc = { workspace = true, features = ["lz4"] }
arrow-schema = { workspace = true }
arrow-select = { workspace = true }
async-backtrace = { workspace = true }
base64 = { workspace = true }
borsh = { workspace = true, features = ["derive"] }
bumpalo = { workspace = true }
comfy-table = { workspace = true }
databend-common-ast = { workspace = true }
databend-common-base = { workspace = true }
databend-common-column = { workspace = true }
databend-common-datavalues = { workspace = true }
databend-common-exception = { workspace = true }
databend-common-grpc = { workspace = true }
databend-common-hashtable = { workspace = true }
databend-common-io = { workspace = true }
databend-common-metrics = { workspace = true }
educe = { workspace = true }
either = { workspace = true }
enum-as-inner = { workspace = true }
ethnum = { workspace = true, features = ["serde", "macros"] }
fastrace = { workspace = true }
futures = { workspace = true }
geo = { workspace = true }
geozero = { workspace = true }
hex = { workspace = true }
hyper-util = { workspace = true }
itertools = { workspace = true }
jiff = { workspace = true }
jsonb = { workspace = true }
lexical-core = { workspace = true }
log = { workspace = true }
match-template = { workspace = true }
memchr = { workspace = true, default-features = false }
micromarshal = { workspace = true }
num-bigint = { workspace = true }
num-traits = { workspace = true }
rand = { workspace = true }
rand_distr = { workspace = true }
recursive = { workspace = true }
roaring = { workspace = true, features = ["serde"] }
rust_decimal = { workspace = true }
rustls = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
strength_reduce = { workspace = true }
terminal_size = { workspace = true }
tonic = { workspace = true }
typetag = { workspace = true }
unicode-segmentation = { workspace = true }

[dev-dependencies]
arrow-ord = { workspace = true }
divan = { workspace = true }
goldenfile = { workspace = true }
pretty_assertions = { workspace = true }
proptest = { workspace = true }
rand = { workspace = true }

[[bench]]
name = "bench"
harness = false

[lints]
workspace = true
