## Copyright 2023 Databend Cloud
##
## Licensed under the Elastic License, Version 2.0 (the "License");
## you may not use this file except in compliance with the License.
## You may obtain a copy of the License at
##
##     https://www.elastic.co/licensing/elastic-license
##
## Unless required by applicable law or agreed to in writing, software
## distributed under the License is distributed on an "AS IS" BASIS,
## WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
## See the License for the specific language governing permissions and
## limitations under the License.

statement ok
drop database if exists test_index

statement ok
create database test_index

statement ok
use test_index


statement ok
DROP AGGREGATING INDEX IF EXISTS testi;

statement ok
DROP AGGREGATING INDEX IF EXISTS testi2;

statement ok
CREATE TABLE t (a int, b int, c int)

statement ok
INSERT INTO t VALUES (1,1,4), (1,2,1), (1,2,4), (2,2,5)

statement ok
CREATE AGGREGATING INDEX testi AS SELECT b, MAX(a), SUM(a) from t WHERE c > 1 GROUP BY b

statement ok
CREATE AGGREGATING INDEX testi2 AS SELECT MAX(a), MIN(b), AVG(c) from t

query I
SELECT b from t WHERE c > 1 GROUP BY b ORDER BY b
----
1
2

query II
SELECT b, SUM(a) from t WHERE c > 1 GROUP BY b ORDER BY b
----
1 1
2 3

query IIT
SELECT MAX(a), MIN(b), AVG(c) from t
----
2 1 3.5

statement ok
REFRESH AGGREGATING INDEX testi

statement ok
REFRESH AGGREGATING INDEX testi2

query I
SELECT b from t WHERE c > 1 GROUP BY b ORDER BY b
----
1
2

query II
SELECT b, SUM(a) from t WHERE c > 1 GROUP BY b ORDER BY b
----
1 1
2 3

query IIR
SELECT MAX(a), MIN(b), AVG(c) from t
----
2 1 3.5

statement ok
CREATE or REPLACE FUNCTION weighted_avg (INT, INT) STATE {sum INT, weight INT} RETURNS FLOAT
LANGUAGE javascript AS $$
export function create_state() {
    return {sum: 0, weight: 0};
}
export function accumulate(state, value, weight) {
    state.sum += value * weight;
    state.weight += weight;
    return state;
}
export function retract(state, value, weight) {
    state.sum -= value * weight;
    state.weight -= weight;
    return state;
}
export function merge(state1, state2) {
    state1.sum += state2.sum;
    state1.weight += state2.weight;
    return state1;
}
export function finish(state) {
    return state.sum / state.weight;
}
$$;

query IIR
SELECT MAX(a), MIN(b), weighted_avg(a,b) from t group by b;
----
2 2 1.3333334
1 1 1.0

query IIR
SELECT MAX(a), MIN(b), weighted_avg(a,b) from t;
----
2 1 1.2857143
